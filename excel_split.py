import os
import sys
import re
import win32com.client
from pathlib import Path

# 目标目录
source_dir = "ERDOS"
output_dir = "ERDOS_拆分结果"

# 确保输出目录存在
os.makedirs(output_dir, exist_ok=True)

# 获取当前目录的绝对路径
current_dir = os.path.abspath(".")
source_path = os.path.join(current_dir, source_dir)
output_path = os.path.join(current_dir, output_dir)

# 启动Excel应用程序
try:
    excel = win32com.client.Dispatch("Excel.Application")
    excel.Visible = False  # 不显示Excel窗口
    excel.DisplayAlerts = False  # 不显示警告对话框
    
    print(f"正在处理文件夹：{source_path}")
    
    # 获取目录下所有Excel文件
    excel_files = [f for f in os.listdir(source_path) if f.endswith('.xls') or f.endswith('.xlsx')]
    
    for excel_file in excel_files:
        file_path = os.path.join(source_path, excel_file)
        print(f"正在处理文件：{excel_file}")
        
        # 从文件名中提取月份信息
        month_match = re.search(r'(\d+)月', excel_file)
        if month_match:
            month = month_match.group(1)
        else:
            month = "未知月份"
        
        # 打开工作簿
        workbook = excel.Workbooks.Open(file_path)
        
        # 处理每个工作表
        for sheet in workbook.Sheets:
            sheet_name = sheet.Name
            print(f"  - 处理工作表：{sheet_name}")
            
            # 创建新的文件名，包含原始文件名、sheet名称和月份
            base_filename = os.path.splitext(excel_file)[0]
            new_filename = f"{base_filename}_{sheet_name}_{month}月.xlsx"
            new_file_path = os.path.join(output_path, new_filename)
            
            # 复制工作表到新工作簿
            sheet.Copy()
            new_workbook = excel.ActiveWorkbook
            
            # 保存为新文件
            new_workbook.SaveAs(new_file_path)
            new_workbook.Close(SaveChanges=False)
            
            print(f"  - 已保存为：{new_filename}")
        
        # 关闭原工作簿
        workbook.Close(SaveChanges=False)
    
    print("所有Excel文件的sheet已成功拆分!")
    
except Exception as e:
    print(f"发生错误：{str(e)}")
    
finally:
    # 关闭Excel应用程序
    if 'excel' in locals():
        excel.Quit()
        del excel 