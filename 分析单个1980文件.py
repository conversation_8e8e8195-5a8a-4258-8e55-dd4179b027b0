import pandas as pd
import sys
import os
import re

# 设置正确的编码
sys.stdout.reconfigure(encoding='utf-8')

# 定义文件路径
file_path = r'C:\Users\<USER>\Desktop\MSP原表\1980_拆分结果\顾客体验报告-山西-1980-1月_黑龙江哈尔滨远大购物中心南岗店1980合_ZQ723_1月.xlsx'

# 检查文件是否存在
if not os.path.exists(file_path):
    print(f"错误：文件不存在 - {file_path}")
    sys.exit(1)

try:
    print(f"正在分析文件: {os.path.basename(file_path)}")
    
    # 读取Excel文件
    df = pd.read_excel(file_path)
    
    # 根据第一行重命名列
    if len(df) > 0:
        # 获取第一行作为列名
        first_row = df.iloc[0]
        # 找到包含关键词的列
        main_dim_col = None
        sub_dim_col = None
        question_col = None
        max_score_col = None
        actual_score_col = None
        detail_col = None
        
        for i, value in enumerate(first_row):
            if isinstance(value, str):
                if '维度' in value and '子维度' not in value:
                    main_dim_col = i
                elif '子维度' in value:
                    sub_dim_col = i
                elif '问题' in value:
                    question_col = i
                elif '满分' in value:
                    max_score_col = i
                elif '得分' in value:
                    actual_score_col = i
                elif '详细描述' in value:
                    detail_col = i
        
        # 创建新的列名映射
        new_columns = {}
        for i, col in enumerate(df.columns):
            if i == main_dim_col:
                new_columns[col] = '主维度'
            elif i == sub_dim_col:
                new_columns[col] = '子维度'
            elif i == question_col:
                new_columns[col] = '问题'
            elif i == max_score_col:
                new_columns[col] = '满分'
            elif i == actual_score_col:
                new_columns[col] = '得分'
            elif i == detail_col:
                new_columns[col] = '详细描述'
            else:
                new_columns[col] = f'列{i+1}'
        
        # 重命名列
        df = df.rename(columns=new_columns)
        
        # 跳过第一行（已经用作列名）
        df = df.iloc[1:].reset_index(drop=True)
    
    # 创建字典来存储每个子维度的分数统计
    subdim_stats = {}
    
    # 获取所有唯一的子维度
    last_valid_subdim = None
    last_valid_main_dim = None
    
    # 第一次遍历，收集所有子维度的分数
    for i in range(len(df)):
        row = df.iloc[i]
        
        # 获取主维度
        if '主维度' in df.columns and pd.notna(row['主维度']):
            last_valid_main_dim = row['主维度']
            print(f"\n主维度: {last_valid_main_dim}")
            
        # 获取子维度，如果为空则使用上一个有效值
        if '子维度' in df.columns and pd.notna(row['子维度']):
            last_valid_subdim = row['子维度']
            print(f"\n  子维度: {last_valid_subdim}")
            
            # 初始化子维度统计数据
            if last_valid_subdim not in subdim_stats:
                subdim_stats[last_valid_subdim] = {
                    '主维度': last_valid_main_dim if last_valid_main_dim else '未指定维度',
                    '满分总计': 0,
                    '得分总计': 0,
                    '问题数量': 0,
                    '有效问题数量': 0  # 排除N/A得分的问题
                }
        
        # 如果有问题字段且不是表头，计算分数
        if last_valid_subdim and '问题' in df.columns and pd.notna(row['问题']):
            score_max = row['满分'] if '满分' in df.columns and pd.notna(row['满分']) else 0
            score_actual = row['得分'] if '得分' in df.columns and pd.notna(row['得分']) else None
            
            # 更新统计数据
            subdim_stats[last_valid_subdim]['问题数量'] += 1
            subdim_stats[last_valid_subdim]['满分总计'] += float(score_max) if isinstance(score_max, (int, float)) else 0
            
            if score_actual is not None and score_actual != 'N/A':
                subdim_stats[last_valid_subdim]['得分总计'] += float(score_actual) if isinstance(score_actual, (int, float)) else 0
                subdim_stats[last_valid_subdim]['有效问题数量'] += 1
            
            # 打印问题和分数
            print(f"    问题: {row['问题']}")
            print(f"      满分: {score_max}, 得分: {score_actual}")
    
    # 提取文件名中的店铺名称和月份
    file_name = os.path.basename(file_path)
    
    # 提取月份
    month_match = re.search(r'1980-(\d+)月', file_name)
    month = month_match.group(1) if month_match else '1'
    
    # 提取店铺名称
    store_name_match = re.search(r'1980-\d+月_(.+?)_ZQ(\d+)', file_name)
    if store_name_match:
        store_name = store_name_match.group(1) + '-ZQ' + store_name_match.group(2)
    else:
        # 尝试其他模式提取
        alt_match = re.search(r'_([^_]+)1980[^_]*_ZQ(\d+)', file_name)
        if alt_match:
            store_name = alt_match.group(1) + '1980-ZQ' + alt_match.group(2)
        else:
            # 直接从文件名中提取店铺编号
            zq_match = re.search(r'ZQ(\d+)', file_name)
            if zq_match:
                # 尝试提取店铺名称
                name_match = re.search(r'_([^_]+)_ZQ', file_name)
                if name_match:
                    store_name = name_match.group(1) + '-ZQ' + zq_match.group(1)
                else:
                    store_name = '未知店铺-ZQ' + zq_match.group(1)
            else:
                store_name = '未知店铺'  # 默认值
    
    # 打印文件信息
    print(f"\n\n文件信息:")
    print(f"月份: {month}")
    print(f"店铺名称: {store_name}")
    
    # 打印子维度得分率
    print(f"\n子维度得分率汇总:")
    print("="*60)
    print(f"{'子维度':<20} | {'满分总计':<10} | {'得分总计':<10} | {'得分率':<10}")
    print("-"*60)
    
    for subdim, stats in subdim_stats.items():
        percentage = (stats['得分总计'] / stats['满分总计']) * 100 if stats['满分总计'] > 0 else 0
        print(f"{subdim:<20} | {stats['满分总计']:<10.1f} | {stats['得分总计']:<10.1f} | {percentage:<10.1f}%")
    
    print("="*60)

except Exception as e:
    print(f"处理文件时出错: {str(e)}")
