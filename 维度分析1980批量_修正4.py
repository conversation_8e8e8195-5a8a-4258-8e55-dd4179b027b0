import pandas as pd
import sys
import os
import re
import glob
from datetime import datetime

# 设置正确的编码
sys.stdout.reconfigure(encoding='utf-8')

# 定义输入目录和输出目录
input_dir = r'C:\Users\<USER>\Desktop\MSP原表\1980_拆分结果'
output_dir = r'C:\Users\<USER>\Desktop\MSP原表\分析结果'
os.makedirs(output_dir, exist_ok=True)
current_time = datetime.now().strftime('%Y%m%d_%H%M%S')
output_file = os.path.join(output_dir, f'1980维度分析汇总_{current_time}.xlsx')

# 定义1980文件中的所有子维度
all_subdims = [
    '店铺情况', '欢迎顾客', '接近顾客', '了解需求', 
    '产品推荐', '搭配', '试穿礼仪', '处理异议', 
    '促进成交', '付货流程', '感谢顾客', '打通', '包装标准'
]

# 定义1980文件中的子维度和它们在报告中的显示名称的映射
subdim_mapping = {
    '店铺情况': '店铺情况',
    '欢迎顾客': '欢迎顾客',
    '接近顾客': '破冰',
    '了解需求': '了解需求',
    '需求3问': '了解需求',
    '需求': '了解需求',
    '产品推荐': '产品推荐',
    '搭配': '搭配推荐',
    '试穿礼仪': '试穿体验',
    '处理异议': '异议处理',
    '促进成交': '引导成交',
    '付货流程': '结账付货',
    '感谢顾客': '感谢顾客',
    '打通': '线上引流',
    '包装标准': '包装标准'
}

# 创建反向映射，用于标准化子维度名称
reverse_subdim_mapping = {}
for orig, display in subdim_mapping.items():
    if display not in reverse_subdim_mapping:
        reverse_subdim_mapping[display] = []
    reverse_subdim_mapping[display].append(orig)

# 定义需要显示的子维度顺序
subdim_order = [
    '店铺情况', '欢迎顾客', '破冰', '了解需求', 
    '产品推荐', '搭配推荐', '试穿体验', '异议处理', 
    '引导成交', '结账付货', '感谢顾客', '线上引流', '包装标准'
]

# 准备汇总数据列表
all_results = []

# 获取所有Excel文件
excel_files = glob.glob(os.path.join(input_dir, '*1980*.xlsx'))
print(f"找到 {len(excel_files)} 个1980 Excel文件进行处理...")

# 处理每个文件
for file_idx, file_path in enumerate(excel_files):
    try:
        print(f"\n处理文件 {file_idx+1}/{len(excel_files)}: {os.path.basename(file_path)}")

        # 读取Excel文件
        df = pd.read_excel(file_path)
        
        # 根据第一行重命名列
        if len(df) > 0:
            # 获取第一行作为列名
            first_row = df.iloc[0]
            # 找到包含关键词的列
            main_dim_col = None
            sub_dim_col = None
            question_col = None
            max_score_col = None
            actual_score_col = None
            detail_col = None
            
            for i, value in enumerate(first_row):
                if isinstance(value, str):
                    if '维度' in value and '子维度' not in value:
                        main_dim_col = i
                    elif '子维度' in value:
                        sub_dim_col = i
                    elif '问题' in value:
                        question_col = i
                    elif '满分' in value:
                        max_score_col = i
                    elif '得分' in value:
                        actual_score_col = i
                    elif '详细描述' in value:
                        detail_col = i
            
            # 创建新的列名映射
            new_columns = {}
            for i, col in enumerate(df.columns):
                if i == main_dim_col:
                    new_columns[col] = '主维度'
                elif i == sub_dim_col:
                    new_columns[col] = '子维度'
                elif i == question_col:
                    new_columns[col] = '问题'
                elif i == max_score_col:
                    new_columns[col] = '满分'
                elif i == actual_score_col:
                    new_columns[col] = '得分'
                elif i == detail_col:
                    new_columns[col] = '详细描述'
                else:
                    new_columns[col] = f'列{i+1}'
            
            # 重命名列
            df = df.rename(columns=new_columns)
            
            # 跳过第一行（已经用作列名）
            df = df.iloc[1:].reset_index(drop=True)
        
        # 创建字典来存储每个子维度的分数统计
        subdim_stats = {}
        
        # 获取所有唯一的子维度
        last_valid_subdim = None
        last_valid_main_dim = None
        
        # 第一次遍历，收集所有子维度的分数
        for i in range(len(df)):
            row = df.iloc[i]
            
            # 获取主维度
            if '主维度' in df.columns and pd.notna(row['主维度']) and row['主维度'] != '主维度':
                last_valid_main_dim = row['主维度']
                # 打印主维度，用于调试
                print(f"    检测到主维度: '{last_valid_main_dim}'")
                
                # 特殊处理"了解需求"主维度
                if isinstance(last_valid_main_dim, str) and '了解需求' in last_valid_main_dim:
                    # 如果主维度是"了解需求"，创建同名子维度以确保不遗漏
                    if '了解需求' not in subdim_stats:
                        subdim_stats['了解需求'] = {
                            '主维度': last_valid_main_dim,
                            '满分总计': 0,
                            '得分总计': 0,
                            '问题数量': 0,
                            '有效问题数量': 0
                        }
                        print(f"    从主维度创建了解需求子维度")
                
            # 获取子维度，如果为空则使用上一个有效值
            if '子维度' in df.columns and pd.notna(row['子维度']) and row['子维度'] != '子维度':
                last_valid_subdim = row['子维度']
                print(f"    检测到子维度: '{last_valid_subdim}'")
                
                # 规范化子维度名称 - 特别处理"了解需求"相关的名称
                if isinstance(last_valid_subdim, str):
                    cleaned_subdim = last_valid_subdim.strip()
                    
                    # 特殊处理"了解需求"相关的维度
                    if last_valid_main_dim == '了解需求' or '了解需求' in cleaned_subdim or ('需求' in cleaned_subdim and '总分' not in cleaned_subdim):
                        print(f"    发现需求相关维度: '{cleaned_subdim}', 主维度: '{last_valid_main_dim}'")
                        
                        # 确保"了解需求"维度存在
                        if '了解需求' not in subdim_stats:
                            subdim_stats['了解需求'] = {
                                '主维度': last_valid_main_dim if last_valid_main_dim else '未指定维度',
                                '满分总计': 0,
                                '得分总计': 0,
                                '问题数量': 0,
                                '有效问题数量': 0
                            }
                        
                        # 如果子维度本身包含"了解需求"字样，使用该子维度，否则仍使用原始子维度
                        if '了解需求' in cleaned_subdim:
                            last_valid_subdim = '了解需求'
                
                # 初始化子维度统计数据
                if last_valid_subdim not in subdim_stats:
                    subdim_stats[last_valid_subdim] = {
                        '主维度': last_valid_main_dim if last_valid_main_dim else '未指定维度',
                        '满分总计': 0,
                        '得分总计': 0,
                        '问题数量': 0,
                        '有效问题数量': 0  # 排除N/A得分的问题
                    }
            
            # 如果有问题字段且不是表头，计算分数
            if last_valid_subdim and '问题' in df.columns and pd.notna(row['问题']) and row['问题'] != '问题':
                score_max = row['满分'] if '满分' in df.columns and pd.notna(row['满分']) else 0
                score_actual = row['得分'] if '得分' in df.columns and pd.notna(row['得分']) and row['得分'] != 'N/A' else None
                
                # 尝试将分数转换为数值
                try:
                    if isinstance(score_max, str):
                        if score_max.lower() == 'input' or score_max.lower() == '输入':
                            score_max_value = 0
                        else:
                            score_max_value = float(score_max) if pd.notna(score_max) else 0
                    else:
                        score_max_value = float(score_max) if pd.notna(score_max) else 0
                except (ValueError, TypeError):
                    score_max_value = 0
                
                try:
                    if score_actual is not None and score_actual != 'N/A':
                        if isinstance(score_actual, str):
                            if score_actual.lower() == 'input' or score_actual.lower() == '输入':
                                score_actual_value = None
                            else:
                                score_actual_value = float(score_actual) if pd.notna(score_actual) else None
                        else:
                            score_actual_value = float(score_actual) if pd.notna(score_actual) else None
                    else:
                        score_actual_value = None
                except (ValueError, TypeError):
                    score_actual_value = None
                
                # 更新当前子维度的统计数据
                subdim_stats[last_valid_subdim]['问题数量'] += 1
                subdim_stats[last_valid_subdim]['满分总计'] += score_max_value
                
                if score_actual_value is not None:
                    subdim_stats[last_valid_subdim]['得分总计'] += score_actual_value
                    subdim_stats[last_valid_subdim]['有效问题数量'] += 1
                
                # 如果主维度是"了解需求"，但子维度不是，则也统计到"了解需求"
                if isinstance(last_valid_main_dim, str) and '了解需求' in last_valid_main_dim and last_valid_subdim != '了解需求':
                    # 确保"了解需求"子维度存在
                    if '了解需求' not in subdim_stats:
                        subdim_stats['了解需求'] = {
                            '主维度': last_valid_main_dim,
                            '满分总计': 0,
                            '得分总计': 0,
                            '问题数量': 0,
                            '有效问题数量': 0
                        }
                    
                    # 更新"了解需求"子维度的统计数据
                    subdim_stats['了解需求']['问题数量'] += 1
                    subdim_stats['了解需求']['满分总计'] += score_max_value
                    
                    if score_actual_value is not None:
                        subdim_stats['了解需求']['得分总计'] += score_actual_value
                        subdim_stats['了解需求']['有效问题数量'] += 1
                    
                    print(f"    为了解需求维度添加问题分数: 满分={score_max_value}, 得分={score_actual_value}")
        
        # 提取文件名中的店铺名称和月份
        file_name = os.path.basename(file_path)
        
        # 提取月份
        month_match = re.search(r'1980-(\d+)月', file_name)
        month = month_match.group(1) if month_match else '1'
        
        # 提取店铺名称 - 使用与原始批量脚本相同的方法
        store_name_match = re.search(r'1980-\d+月_(.+?)_ZQ(\d+)', file_name)
        if store_name_match:
            store_name = store_name_match.group(1) + '-ZQ' + store_name_match.group(2)
        else:
            # 尝试其他模式提取
            alt_match = re.search(r'_([^_]+)-ZQ(\d+)_', file_name)
            if alt_match:
                store_name = alt_match.group(1) + '-ZQ' + alt_match.group(2)
            else:
                # 尝试第三种模式
                third_match = re.search(r'_([^_]+)1980[^_]*_ZQ(\d+)', file_name)
                if third_match:
                    store_name = third_match.group(1) + '-ZQ' + third_match.group(2)
                else:
                    # 直接从文件名中提取店铺编号
                    zq_match = re.search(r'ZQ(\d+)', file_name)
                    if zq_match:
                        # 尝试提取店铺名称
                        name_match = re.search(r'_([^_]+)_ZQ', file_name)
                        if name_match:
                            store_name = name_match.group(1) + '-ZQ' + zq_match.group(1)
                        else:
                            store_name = '未知店铺-ZQ' + zq_match.group(1)
                    else:
                        store_name = '未知店铺'  # 默认值
                        
        # 如果店铺名称仍然未知，尝试从文件内容中提取
        if '未知店铺' in store_name and '详细描述' in df.columns:
            for i, row in df.iterrows():
                if pd.notna(row['详细描述']) and 'ZQ' in str(row['详细描述']):
                    store_info = str(row['详细描述'])
                    zq_match = re.search(r'ZQ(\d+)', store_info)
                    if zq_match:
                        store_code = zq_match.group(1)
                        # 尝试从详细描述中提取店铺名称
                        if '_' in store_info:
                            store_parts = store_info.split('_')
                            if len(store_parts) > 0:
                                store_name = store_parts[0] + '-ZQ' + store_code
                                break
        
        # 处理特殊情况：月份3的ZQ723店铺
        if month == '3' and 'ZQ723' in store_name and '未知店铺' in store_name:
            store_name = '黑龙江哈尔滨远大购物中心南岗店1980合-ZQ723'
        
        # 创建子维度得分率字典，并添加详细调试信息
        subdim_percentages = {}
        print("\n  子维度统计详细信息:")
        for subdim, stats in subdim_stats.items():
            print(f"    {subdim}:")
            print(f"      主维度={stats['主维度']}, 满分总计={stats['满分总计']}, 得分总计={stats['得分总计']}, 问题数量={stats['问题数量']}, 有效问题数量={stats['有效问题数量']}")
            
            if stats['有效问题数量'] > 0 and stats['满分总计'] > 0:
                percentage = (stats['得分总计'] / stats['满分总计']) * 100
                subdim_percentages[subdim] = int(round(percentage))
                print(f"      计算得分率: {percentage:.1f}% → {int(round(percentage))}%")
            else:
                print(f"      跳过计算得分率: 有效问题数量={stats['有效问题数量']}, 满分总计={stats['满分总计']}")
        
        # 检查是否有任何包含"需求"的子维度
        has_need_related_dim = False
        need_related_dims = []
        for subdim in subdim_stats.keys():
            if isinstance(subdim, str) and ('需求' in subdim or '了解' in subdim):
                has_need_related_dim = True
                need_related_dims.append(subdim)
        
        print(f"  文件中包含需求相关维度: {has_need_related_dim}")
        if has_need_related_dim:
            print(f"  需求相关维度列表: {need_related_dims}")
        
        # 准备当前文件的结果行
        result_row = {
            '月份': month,
            '店铺名称': store_name
        }
        
        # 直接处理需求相关维度
        need_related_percentages = []
        for subdim, percentage in subdim_percentages.items():
            if isinstance(subdim, str) and ('需求' in subdim or '了解' in subdim):
                need_related_percentages.append((subdim, percentage))
                print(f"  找到需求相关维度值: {subdim} = {percentage}%")
        
        # 特别处理"了解需求"维度，优先使用精确匹配
        if '了解需求' in subdim_percentages:
            result_row['了解需求'] = subdim_percentages['了解需求']
            print(f"  直接设置'了解需求'值为: {result_row['了解需求']}% (精确匹配)")
        elif need_related_percentages:
            # 如果没有精确匹配但有相关维度，取第一个相关维度的值
            result_row['了解需求'] = need_related_percentages[0][1]
            print(f"  设置'了解需求'值为: {result_row['了解需求']}% (来自 {need_related_percentages[0][0]})")
        
        # 根据定义的顺序填充其他数据
        for display_subdim in subdim_order:
            # 如果已经设置了了解需求，跳过
            if display_subdim == '了解需求' and '了解需求' in result_row:
                continue
            
            # 获取该显示维度对应的所有可能原始维度名称
            original_subdims = reverse_subdim_mapping.get(display_subdim, [])
            
            # 查找任何匹配的原始子维度
            matched = False
            for original_subdim in original_subdims:
                if original_subdim in subdim_percentages:
                    result_row[display_subdim] = subdim_percentages[original_subdim]
                    print(f"    找到匹配: {display_subdim} = {original_subdim} = {subdim_percentages[original_subdim]}%")
                    matched = True
                    break
            
            # 如果没有精确匹配，尝试模糊匹配
            if not matched:
                for actual_subdim, percentage in subdim_percentages.items():
                    # 如果原始维度中包含关键词
                    for original_subdim in original_subdims:
                        if (isinstance(actual_subdim, str) and isinstance(original_subdim, str)):
                            # 标准化字符串进行比较
                            norm_actual = actual_subdim.lower().strip()
                            norm_original = original_subdim.lower().strip()
                            
                            # 常规的包含关系检查
                            if (norm_original in norm_actual or norm_actual in norm_original):
                                result_row[display_subdim] = percentage
                                print(f"    模糊匹配: {display_subdim} ~ {actual_subdim} = {percentage}%")
                                matched = True
                                break
                    
                    if matched:
                        break
            
            # 如果仍未匹配，使用特殊处理
            if not matched:
                # 特殊处理：包装标准可能不存在于1980文件中
                if display_subdim == '包装标准':
                    result_row[display_subdim] = 0
                # 特殊处理：线上引流对应打通
                elif display_subdim == '线上引流' and '打通' in subdim_percentages:
                    result_row[display_subdim] = subdim_percentages['打通']
                else:
                    result_row[display_subdim] = 0
                    print(f"    未找到匹配: {display_subdim} = 0%")
        
        # 将结果添加到汇总列表
        all_results.append(result_row)
        
        # 打印当前文件的结果
        print(f"  月份: {month}, 店铺: {store_name}")
        print(f"  子维度得分率: ", end="")
        for subdim in subdim_order:
            print(f"{subdim}={result_row.get(subdim, 0)}%, ", end="")
        print()
        
    except Exception as e:
        import traceback
        print(f"  处理文件时出错: {str(e)}")
        print(f"  错误详情: {traceback.format_exc()}")

# 如果没有收集到结果，则退出
if not all_results:
    print("\n没有收集到任何结果，请检查输入文件。")
    sys.exit(1)

# 创建汇总数据框
results_df = pd.DataFrame(all_results)

# 定义表头顺序
headers = ['月份', '店铺名称'] + subdim_order

# 确保所有列都存在
for header in headers:
    if header not in results_df.columns:
        results_df[header] = 0

# 按照指定顺序重排列
results_df = results_df[headers]

# 对数据进行排序
# 首先按月份排序，然后按店铺名称排序
results_df['月份'] = pd.to_numeric(results_df['月份'], errors='coerce')
results_df = results_df.sort_values(by=['月份', '店铺名称'])

# 打印汇总表格
print("\n\n子维度得分率汇总表:")
print("="*150)
print(" | ".join(headers))
print("-"*150)

for _, row in results_df.iterrows():
    row_str = [str(row[header]) for header in headers]
    print(" | ".join(row_str))

print("="*150)

# 将结果保存到Excel文件
try:
    with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
        # 保存主数据表
        results_df.to_excel(writer, sheet_name='子维度得分率汇总', index=False)
        
        # 创建按月份汇总的数据
        monthly_summary = results_df.groupby('月份').mean(numeric_only=True).reset_index()
        # 将月份列转换为整数
        monthly_summary['月份'] = monthly_summary['月份'].astype(int)
        # 添加汇总行标签
        monthly_summary.insert(1, '店铺名称', [f'{month}月平均' for month in monthly_summary['月份']])
        monthly_summary.to_excel(writer, sheet_name='按月份汇总', index=False)
        
        # 创建总体平均汇总
        overall_avg = pd.DataFrame([{
            '月份': '',
            '店铺名称': '总体平均',
            **{col: results_df[col].mean() for col in results_df.columns[2:]}
        }])
        overall_avg.to_excel(writer, sheet_name='总体平均', index=False)
        
        # 创建按店铺汇总的数据
        # 先将数据按店铺编号分组
        store_pattern = re.compile(r'ZQ(\d+)')
        
        def extract_store_code(store_name):
            match = store_pattern.search(store_name)
            return match.group(1) if match else '999'
        
        results_df['店铺编号'] = results_df['店铺名称'].apply(extract_store_code)
        store_summary = results_df.groupby('店铺编号').agg({
            '店铺名称': 'first',
            **{col: 'mean' for col in results_df.columns[2:-1]}
        }).reset_index()
        
        # 删除店铺编号列
        store_summary = store_summary.drop(columns=['店铺编号'])
        store_summary.to_excel(writer, sheet_name='按店铺汇总', index=False)
        
    print(f"\n汇总结果已导出到Excel文件: {output_file}")
    print(f"\n已创建以下工作表:\n1. 子维度得分率汇总 - 所有原始数据\n2. 按月份汇总 - 按月份分组的平均分\n3. 按店铺汇总 - 按店铺分组的平均分\n4. 总体平均 - 所有数据的平均值")
except Exception as e:
    print(f'导出Excel时出错: {str(e)}')
