import pandas as pd
import os
import re
import glob
from pathlib import Path
import openpyxl
from openpyxl.styles import Alignment, Font, Border, Side, PatternFill
from openpyxl.utils import get_column_letter
import time

# 指定目录路径
folder_1980 = r"C:\Users\<USER>\Desktop\MSP原表\1980_拆分结果"
folder_erdos = r"C:\Users\<USER>\Desktop\MSP原表\ERDOS_拆分结果"

# 使用时间戳创建唯一的输出文件名
timestamp = time.strftime("%Y%m%d_%H%M%S")
output_1980 = rf"C:\Users\<USER>\Desktop\MSP原表\1980问题与描述汇总_{timestamp}.xlsx"
output_erdos = rf"C:\Users\<USER>\Desktop\MSP原表\ERDOS问题与描述汇总_{timestamp}.xlsx"

# 定义一个函数来清理店铺名称
def clean_store_name(name):
    # 去除"-ZQ数字"部分
    cleaned = re.sub(r'[-\s]*ZQ\d+\s*', '', name)
    # 去除文件名尾部的空格和特殊字符
    cleaned = re.sub(r'[-\s]+$', '', cleaned)
    return cleaned

# 定义一个函数来处理每个文件夹
def process_folder(folder_path, brand_name):
    print(f"\n开始处理 {brand_name} 品牌文件夹: {folder_path}")
    
    # 检查目录是否存在
    if not os.path.exists(folder_path):
        print(f"目录不存在: {folder_path}")
        return None
    
    # 获取目录中的所有Excel文件（排除~$开头的临时文件）
    excel_files = [file for file in glob.glob(os.path.join(folder_path, "*.xlsx")) 
                  if not os.path.basename(file).startswith("~$")]
    print(f"找到 {len(excel_files)} 个Excel文件")
    
    # 创建一个存储每个月每个店铺的问题和描述的字典
    # 格式: {月份: {店铺: {序号: {问题: xx, 得分: xx, 描述: xx}}}}
    month_store_data = {}
    
    # 记录所有找到的月份和店铺
    months_found = set()
    stores_found = set()
    
    # 处理每个Excel文件
    for file_path in excel_files:
        file_name = os.path.basename(file_path)
        print(f"正在处理文件: {file_name}")
        
        try:
            # 从文件名中提取信息
            # 提取月份信息
            month_match = re.search(r'(\d+)月', file_name)
            month = month_match.group(1) if month_match else "未知月份"
            months_found.add(month)
            
            # 提取店铺名称
            store_match = re.search(r'_([^_]+?)(?:_ZQ\d+|_\d+月)', file_name)
            if not store_match:
                # 如果上述匹配失败，尝试另一种匹配模式
                store_match = re.search(r'_(.+?)_\d+月', file_name)
            
            original_store_name = store_match.group(1) if store_match else "未知店铺"
            
            # 清理店铺名称
            store_name = clean_store_name(original_store_name)
            
            if store_name == "未知店铺":
                print(f"  警告：无法从文件名中提取店铺名称: {file_name}")
                continue
            
            # 添加到店铺集合
            stores_found.add(store_name)
            
            # 初始化月份数据结构
            if month not in month_store_data:
                month_store_data[month] = {}
            
            # 初始化店铺数据结构
            if store_name not in month_store_data[month]:
                month_store_data[month][store_name] = {}
            
            # 读取Excel文件，查找标题行
            df_raw = pd.read_excel(file_path, header=None)
            
            # 查找包含"维度"和"得分"等关键词的标题行
            header_row = None
            for i in range(min(5, df_raw.shape[0])):
                row_values = [str(x).lower() if pd.notna(x) else "" for x in df_raw.iloc[i]]
                row_text = " ".join(row_values)
                if "维度" in row_text and ("no" in row_text or "得分" in row_text):
                    header_row = i
                    break
            
            if header_row is None:
                print(f"  未找到标题行，跳过文件: {file_name}")
                continue
            
            # 使用标题行重新读取数据
            df = pd.read_excel(file_path, header=header_row)
            
            # 确定关键列
            columns = list(df.columns)
            dim_col = None    # 维度列
            sub_col = None    # 子维度列
            no_col = None     # 序号列
            quest_col = None  # 问题列
            score_col = None  # 得分列
            desc_col = None   # 详细描述列
            
            # 识别关键列
            for col in columns:
                col_str = str(col).lower()
                if "维度" in col_str and "子" not in col_str:
                    dim_col = col
                elif "子维度" in col_str:
                    sub_col = col
                elif col_str == "no" or "编号" in col_str:
                    no_col = col
                elif "问题" in col_str:
                    quest_col = col
                elif "得分" in col_str:
                    score_col = col
                elif "详细描述" in col_str or "备注" in col_str:
                    desc_col = col
            
            # 确保找到所有需要的列
            if not all([no_col, quest_col, score_col, desc_col]):
                missing_cols = []
                if not no_col: missing_cols.append("序号列")
                if not quest_col: missing_cols.append("问题列")
                if not score_col: missing_cols.append("得分列")
                if not desc_col: missing_cols.append("详细描述列")
                print(f"  未找到所有必要的列: {', '.join(missing_cols)}，跳过文件: {file_name}")
                continue
            
            # 清理数据
            df = df.fillna("")
            
            # 提取数据
            for _, row in df.iterrows():
                if pd.notna(row[no_col]) and row[no_col] != "":
                    no_val = row[no_col]
                    
                    # 获取数据
                    question = row[quest_col] if pd.notna(row[quest_col]) else ""
                    score = row[score_col] if pd.notna(row[score_col]) else ""
                    desc = row[desc_col] if pd.notna(row[desc_col]) else ""
                    
                    # 获取维度和子维度数据（如果有）
                    dimension = row[dim_col] if dim_col and pd.notna(row[dim_col]) else ""
                    sub_dimension = row[sub_col] if sub_col and pd.notna(row[sub_col]) else ""
                    
                    # 只处理有得分的数据
                    if score != "":
                        # 存储数据
                        month_store_data[month][store_name][no_val] = {
                            "维度": dimension,
                            "子维度": sub_dimension,
                            "问题": question,
                            "得分": score,
                            "详细描述": desc
                        }
            
        except Exception as e:
            print(f"  处理文件 {file_name} 时出错: {str(e)}")
    
    # 显示找到的月份和店铺
    months_list = sorted([m for m in months_found if m.isdigit()], key=int)
    print(f"\n{brand_name} 找到的月份: {months_list}")
    
    # 统计每个月的店铺数量
    for month in months_list:
        if month in month_store_data:
            stores_in_month = sorted(month_store_data[month].keys())
            print(f"{month}月有数据的店铺数量: {len(stores_in_month)}")
    
    return {
        "months": months_list,
        "stores": sorted(stores_found),
        "data": month_store_data
    }

# 处理两个品牌的文件夹
data_1980 = process_folder(folder_1980, "1980")
data_erdos = process_folder(folder_erdos, "ERDOS")

# 分别为两个品牌创建汇总Excel
def create_summary_excel(data, output_file, brand_name):
    if not data:
        print(f"没有找到{brand_name}的有效数据，跳过创建汇总表")
        return
    
    print(f"\n正在创建{brand_name}的问题与描述汇总表: {output_file}")
    
    # 创建工作簿
    workbook = openpyxl.Workbook()
    
    # 样式设置
    header_font = Font(bold=True, name='微软雅黑', size=10)
    header_fill = PatternFill(start_color="D9E1F2", end_color="D9E1F2", fill_type="solid")
    month_fill = PatternFill(start_color="E2EFDA", end_color="E2EFDA", fill_type="solid")
    border = Border(
        left=Side(style='thin'), 
        right=Side(style='thin'), 
        top=Side(style='thin'), 
        bottom=Side(style='thin')
    )
    center_alignment = Alignment(horizontal='center', vertical='center', wrap_text=True)
    
    # 为每个月创建一个工作表
    for month in data["months"]:
        if month not in data["data"]:
            continue
        
        # 创建工作表
        worksheet = workbook.create_sheet(title=f"{month}月")
        
        # 设置列宽
        worksheet.column_dimensions['A'].width = 8   # 序号
        worksheet.column_dimensions['B'].width = 15  # 店铺
        worksheet.column_dimensions['C'].width = 15  # 维度
        worksheet.column_dimensions['D'].width = 15  # 子维度
        worksheet.column_dimensions['E'].width = 40  # 问题
        worksheet.column_dimensions['F'].width = 8   # 得分
        worksheet.column_dimensions['G'].width = 50  # 详细描述
        
        # 添加标题行
        headers = ["序号", "店铺", "维度", "子维度", "问题", "得分", "详细描述"]
        for col, header in enumerate(headers, start=1):
            cell = worksheet.cell(row=1, column=col)
            cell.value = header
            cell.font = header_font
            cell.fill = header_fill
            cell.border = border
            cell.alignment = center_alignment
        
        # 填充数据
        row_num = 2
        for store in sorted(data["data"][month].keys()):
            for no_val in sorted(data["data"][month][store].keys(), key=lambda x: float(x) if isinstance(x, str) and x.replace('.', '', 1).isdigit() else float('inf')):
                item_data = data["data"][month][store][no_val]
                
                # 添加数据
                worksheet.cell(row=row_num, column=1).value = no_val
                worksheet.cell(row=row_num, column=2).value = store
                worksheet.cell(row=row_num, column=3).value = item_data.get("维度", "")
                worksheet.cell(row=row_num, column=4).value = item_data.get("子维度", "")
                worksheet.cell(row=row_num, column=5).value = item_data.get("问题", "")
                worksheet.cell(row=row_num, column=6).value = item_data.get("得分", "")
                worksheet.cell(row=row_num, column=7).value = item_data.get("详细描述", "")
                
                # 设置样式
                for col in range(1, 8):
                    cell = worksheet.cell(row=row_num, column=col)
                    cell.border = border
                    if col in [1, 2, 3, 4, 6]:  # 居中: 序号, 店铺, 维度, 子维度, 得分
                        cell.alignment = center_alignment
                    else:  # 左对齐并允许换行: 问题, 详细描述
                        cell.alignment = Alignment(vertical='center', wrap_text=True)
                
                row_num += 1
    
    # 创建汇总工作表
    summary_sheet = workbook.create_sheet(title="汇总", index=0)
    
    # 设置汇总工作表的列宽
    summary_sheet.column_dimensions['A'].width = 8   # 序号
    summary_sheet.column_dimensions['B'].width = 15  # 月份
    summary_sheet.column_dimensions['C'].width = 15  # 店铺
    summary_sheet.column_dimensions['D'].width = 15  # 维度
    summary_sheet.column_dimensions['E'].width = 15  # 子维度
    summary_sheet.column_dimensions['F'].width = 40  # 问题
    summary_sheet.column_dimensions['G'].width = 8   # 得分
    summary_sheet.column_dimensions['H'].width = 50  # 详细描述
    
    # 添加汇总标题行
    summary_headers = ["序号", "月份", "店铺", "维度", "子维度", "问题", "得分", "详细描述"]
    for col, header in enumerate(summary_headers, start=1):
        cell = summary_sheet.cell(row=1, column=col)
        cell.value = header
        cell.font = header_font
        cell.fill = header_fill
        cell.border = border
        cell.alignment = center_alignment
    
    # 填充汇总数据
    row_num = 2
    for month in data["months"]:
        if month not in data["data"]:
            continue
            
        for store in sorted(data["data"][month].keys()):
            for no_val in sorted(data["data"][month][store].keys(), key=lambda x: float(x) if isinstance(x, str) and x.replace('.', '', 1).isdigit() else float('inf')):
                item_data = data["data"][month][store][no_val]
                
                # 添加数据
                summary_sheet.cell(row=row_num, column=1).value = no_val
                summary_sheet.cell(row=row_num, column=2).value = f"{month}月"
                summary_sheet.cell(row=row_num, column=3).value = store
                summary_sheet.cell(row=row_num, column=4).value = item_data.get("维度", "")
                summary_sheet.cell(row=row_num, column=5).value = item_data.get("子维度", "")
                summary_sheet.cell(row=row_num, column=6).value = item_data.get("问题", "")
                summary_sheet.cell(row=row_num, column=7).value = item_data.get("得分", "")
                summary_sheet.cell(row=row_num, column=8).value = item_data.get("详细描述", "")
                
                # 设置样式
                for col in range(1, 9):
                    cell = summary_sheet.cell(row=row_num, column=col)
                    cell.border = border
                    if col in [1, 2, 3, 4, 5, 7]:  # 居中: 序号, 月份, 店铺, 维度, 子维度, 得分
                        cell.alignment = center_alignment
                    else:  # 左对齐并允许换行: 问题, 详细描述
                        cell.alignment = Alignment(vertical='center', wrap_text=True)
                
                row_num += 1
    
    # 创建问题汇总工作表 - 收集所有独特的问题及其描述
    question_sheet = workbook.create_sheet(title="问题汇总")
    
    # 设置列宽
    question_sheet.column_dimensions['A'].width = 8   # 序号
    question_sheet.column_dimensions['B'].width = 40  # 问题
    question_sheet.column_dimensions['C'].width = 15  # 店铺数量
    question_sheet.column_dimensions['D'].width = 30  # 店铺列表
    question_sheet.column_dimensions['E'].width = 50  # 详细描述汇总
    
    # 添加标题行
    question_headers = ["序号", "问题", "店铺数量", "店铺列表", "详细描述汇总"]
    for col, header in enumerate(question_headers, start=1):
        cell = question_sheet.cell(row=1, column=col)
        cell.value = header
        cell.font = header_font
        cell.fill = header_fill
        cell.border = border
        cell.alignment = center_alignment
    
    # 收集所有问题数据
    question_data = {}  # {问题序号: {问题: xx, 店铺: set(), 描述: set()}}
    
    for month in data["data"]:
        for store in data["data"][month]:
            for no_val in data["data"][month][store]:
                item = data["data"][month][store][no_val]
                
                if no_val not in question_data:
                    question_data[no_val] = {
                        "问题": item["问题"],
                        "店铺": set(),
                        "描述": set()
                    }
                
                question_data[no_val]["店铺"].add(store)
                if item["详细描述"]:
                    question_data[no_val]["描述"].add(item["详细描述"])
    
    # 填充问题汇总表
    row_num = 2
    for no_val in sorted(question_data.keys(), key=lambda x: float(x) if isinstance(x, str) and x.replace('.', '', 1).isdigit() else float('inf')):
        item = question_data[no_val]
        
        # 添加数据
        question_sheet.cell(row=row_num, column=1).value = no_val
        question_sheet.cell(row=row_num, column=2).value = item["问题"]
        question_sheet.cell(row=row_num, column=3).value = len(item["店铺"])
        question_sheet.cell(row=row_num, column=4).value = ", ".join(sorted(item["店铺"]))
        question_sheet.cell(row=row_num, column=5).value = "\n\n".join(sorted(item["描述"]))
        
        # 设置样式
        for col in range(1, 6):
            cell = question_sheet.cell(row=row_num, column=col)
            cell.border = border
            if col in [1, 3]:  # 居中: 序号, 店铺数量
                cell.alignment = center_alignment
            else:  # 左对齐并允许换行: 问题, 店铺列表, 详细描述汇总
                cell.alignment = Alignment(vertical='center', wrap_text=True)
        
        row_num += 1
    
    # 删除默认创建的工作表
    if "Sheet" in workbook.sheetnames:
        del workbook["Sheet"]
    
    # 保存工作簿
    workbook.save(output_file)
    print(f"{brand_name}问题与描述汇总表创建完成: {output_file}")

# 为两个品牌创建汇总Excel
if data_1980:
    create_summary_excel(data_1980, output_1980, "1980")
if data_erdos:
    create_summary_excel(data_erdos, output_erdos, "ERDOS")

print("\n处理完成!") 