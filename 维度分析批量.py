import pandas as pd
import sys
import os
import re
import glob
from datetime import datetime

# 设置正确的编码
sys.stdout.reconfigure(encoding='utf-8')

# 定义输入目录和输出目录
input_dir = r'C:\Users\<USER>\Desktop\MSP原表\ERDOS_拆分结果'
output_dir = r'C:\Users\<USER>\Desktop\MSP原表\分析结果'
os.makedirs(output_dir, exist_ok=True)
current_time = datetime.now().strftime('%Y%m%d_%H%M%S')
output_file = os.path.join(output_dir, f'维度分析汇总_{current_time}.xlsx')

# 定义需要显示的子维度顺序
subdim_order = [
    '包装标准', '店铺情况', '欢迎顶客', '破冰', 
    '需求3问', '产品2推', '搭配2备', '试穿体验', 
    '异议处理&引导成交', '付货', '感谢顶客'
]

# 定义子维度名称的可能变体映射
subdim_variants = {
    '欢迎顶客': ['欢迎顶客', '欢迎顾客', '欢迎'],
    '异议处理&引导成交': ['异议处理&引导成交', '异议处理', '引导成交']
}

# 创建反向映射以便规范化子维度名称
subdim_normalize = {}
for standard, variants in subdim_variants.items():
    for variant in variants:
        subdim_normalize[variant] = standard

# 准备汇总数据列表
all_results = []

# 获取所有Excel文件
excel_files = glob.glob(os.path.join(input_dir, '*.xlsx'))
print(f"找到 {len(excel_files)} 个Excel文件进行处理...")

# 处理每个文件
for file_idx, file_path in enumerate(excel_files):
    try:
        print(f"\n处理文件 {file_idx+1}/{len(excel_files)}: {os.path.basename(file_path)}")

        # 读取Excel文件
        df = pd.read_excel(file_path)
        
        # 将列名重命名为更易于理解的名称
        new_columns = ['主维度', '子维度', 'No', '问题', '满分', '得分', '详细描述']
        if len(df.columns) >= len(new_columns):
            df.columns = new_columns + list(df.columns[len(new_columns):])
        
        # 创建字典来存储每个子维度的分数统计
        subdim_stats = {}
        
        # 获取所有唯一的子维度
        last_valid_subdim = None
        last_valid_main_dim = None
        
        # 第一次遍历，收集所有子维度的分数
        for i in range(len(df)):
            row = df.iloc[i]
            
            # 获取主维度
            if pd.notna(row['主维度']) and row['主维度'] != '主维度':
                last_valid_main_dim = row['主维度']
                
            # 获取子维度，如果为空则使用上一个有效值
            if pd.notna(row['子维度']) and row['子维度'] != '子维度':
                last_valid_subdim = row['子维度']
                
                # 规范化子维度名称
                if last_valid_subdim in subdim_normalize:
                    last_valid_subdim = subdim_normalize[last_valid_subdim]
                
                # 初始化子维度统计数据
                if last_valid_subdim not in subdim_stats:
                    subdim_stats[last_valid_subdim] = {
                        '主维度': last_valid_main_dim if last_valid_main_dim else '未指定维度',
                        '满分总计': 0,
                        '得分总计': 0,
                        '问题数量': 0,
                        '有效问题数量': 0  # 排除N/A得分的问题
                    }
            
            # 如果有问题字段且不是表头，计算分数
            if last_valid_subdim and pd.notna(row['问题']) and row['问题'] != '问题':
                score_max = row['满分'] if pd.notna(row['满分']) else 0
                score_actual = row['得分'] if pd.notna(row['得分']) and row['得分'] != 'N/A' else None
                
                # 更新统计数据
                subdim_stats[last_valid_subdim]['问题数量'] += 1
                subdim_stats[last_valid_subdim]['满分总计'] += float(score_max) if isinstance(score_max, (int, float)) else 0
                
                if score_actual is not None and score_actual != 'N/A':
                    subdim_stats[last_valid_subdim]['得分总计'] += float(score_actual) if isinstance(score_actual, (int, float)) else 0
                    subdim_stats[last_valid_subdim]['有效问题数量'] += 1
        
        # 提取文件名中的店铺名称和月份
        file_name = os.path.basename(file_path)
        
        # 提取月份
        month_match = re.search(r'ERDOS-(\d+)月', file_name)
        month = month_match.group(1) if month_match else '1'
        
        # 提取店铺名称
        store_name_match = re.search(r'ERDOS-\d+月_(.+?)_ZQ(\d+)', file_name)
        if store_name_match:
            store_name = store_name_match.group(1) + '-ZQ' + store_name_match.group(2)
        else:
            # 尝试其他模式提取
            alt_match = re.search(r'_([^_]+)-ZQ(\d+)_', file_name)
            if alt_match:
                store_name = alt_match.group(1) + '-ZQ' + alt_match.group(2)
            else:
                # 直接从文件名中提取店铺编号
                zq_match = re.search(r'ZQ(\d+)', file_name)
                if zq_match:
                    # 尝试提取店铺名称
                    name_match = re.search(r'_([^_]+)_ZQ', file_name)
                    if name_match:
                        store_name = name_match.group(1) + '-ZQ' + zq_match.group(1)
                    else:
                        store_name = '未知店铺-ZQ' + zq_match.group(1)
                else:
                    store_name = '未知店铺'  # 默认值
        
        # 创建子维度得分率字典
        subdim_percentages = {}
        for subdim, stats in subdim_stats.items():
            if stats['有效问题数量'] > 0:
                percentage = (stats['得分总计'] / stats['满分总计']) * 100 if stats['满分总计'] > 0 else 0
                subdim_percentages[subdim] = int(round(percentage))
        
        # 打印所有检测到的子维度名称及其得分率（用于调试）
        print(f"  检测到的子维度及得分率:")
        for subdim, percentage in subdim_percentages.items():
            print(f"    - {subdim}: {percentage}%")
            
        # 打印变量映射用于调试
        print(f"  子维度名称规范化映射: {subdim_normalize}")
        
        # 准备当前文件的结果行
        result_row = {
            '月份': month,
            '店铺名称': store_name
        }
        
        # 根据定义的顺序填充数据
        for subdim in subdim_order:
            if subdim == '包装标准':
                result_row[subdim] = 0  # 包装标准默认为0
            elif subdim == '异议处理&引导成交':
                column_name = '处理引导'
                result_row[column_name] = subdim_percentages.get(subdim, 0)
            elif subdim in subdim_percentages:
                result_row[subdim] = subdim_percentages[subdim]
            else:
                result_row[subdim] = 0  # 若没有找到对应的子维度，则设置为0
        
        # 将结果添加到汇总列表
        all_results.append(result_row)
        
        # 打印当前文件的结果
        print(f"  月份: {month}, 店铺: {store_name}")
        print(f"  子维度得分率: ", end="")
        for subdim in subdim_order:
            if subdim == '异议处理&引导成交':
                print(f"处理引导={result_row.get('处理引导', 0)}%, ", end="")
            else:
                print(f"{subdim}={result_row.get(subdim, 0)}%, ", end="")
        print()
        
    except Exception as e:
        print(f"  处理文件时出错: {str(e)}")

# 如果没有收集到结果，则退出
if not all_results:
    print("\n没有收集到任何结果，请检查输入文件。")
    sys.exit(1)

# 创建汇总数据框
results_df = pd.DataFrame(all_results)

# 定义表头顺序
headers = ['月份', '店铺名称', '包装标准', '店铺情况', '欢迎顶客', '破冰', 
           '需求3问', '产品2推', '搭配2备', '试穿体验', '处理引导', '付货', '感谢顶客']

# 确保所有列都存在
for header in headers:
    if header not in results_df.columns:
        results_df[header] = 0

# 按照指定顺序重排列
results_df = results_df[headers]

# 打印汇总表格
print("\n\n子维度得分率汇总表:")
print("="*150)
print(" | ".join(headers))
print("-"*150)

for _, row in results_df.iterrows():
    row_str = [str(row[header]) for header in headers]
    print(" | ".join(row_str))

print("="*150)

# 对数据进行排序
# 首先按月份排序，然后按店铺名称排序
results_df['月份'] = pd.to_numeric(results_df['月份'], errors='coerce')
results_df = results_df.sort_values(by=['月份', '店铺名称'])

# 将结果保存到Excel文件
try:
    with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
        # 保存主数据表
        results_df.to_excel(writer, sheet_name='子维度得分率汇总', index=False)
        
        # 创建按月份汇总的数据
        monthly_summary = results_df.groupby('月份').mean(numeric_only=True).reset_index()
        # 将月份列转换为整数
        monthly_summary['月份'] = monthly_summary['月份'].astype(int)
        # 添加汇总行标签
        monthly_summary.insert(1, '店铺名称', [f'{month}月平均' for month in monthly_summary['月份']])
        monthly_summary.to_excel(writer, sheet_name='按月份汇总', index=False)
        
        # 创建总体平均汇总
        overall_avg = pd.DataFrame([{
            '月份': '',
            '店铺名称': '总体平均',
            **{col: results_df[col].mean() for col in results_df.columns[2:]}
        }])
        overall_avg.to_excel(writer, sheet_name='总体平均', index=False)
        
        # 创建按店铺汇总的数据
        # 先将数据按店铺编号分组
        store_pattern = re.compile(r'ZQ(\d+)')
        
        def extract_store_code(store_name):
            match = store_pattern.search(store_name)
            return match.group(1) if match else '999'
        
        results_df['店铺编号'] = results_df['店铺名称'].apply(extract_store_code)
        store_summary = results_df.groupby('店铺编号').agg({
            '店铺名称': 'first',
            **{col: 'mean' for col in results_df.columns[2:-1]}
        }).reset_index()
        
        # 删除店铺编号列
        store_summary = store_summary.drop(columns=['店铺编号'])
        store_summary.to_excel(writer, sheet_name='按店铺汇总', index=False)
        
    print(f"\n汇总结果已导出到Excel文件: {output_file}")
    print(f"\n已创建以下工作表:\n1. 子维度得分率汇总 - 所有原始数据\n2. 按月份汇总 - 按月份分组的平均分\n3. 按店铺汇总 - 按店铺分组的平均分\n4. 总体平均 - 所有数据的平均值")
except Exception as e:
    print(f'导出Excel时出错: {str(e)}')
