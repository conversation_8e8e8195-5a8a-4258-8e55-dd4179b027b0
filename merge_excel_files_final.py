import pandas as pd
import os
import re
import glob
from pathlib import Path
import openpyxl
from openpyxl.styles import Alignment, Font, Border, Side, PatternFill
from openpyxl.utils import get_column_letter
import time

# 指定目录路径
folder_path = r"C:\Users\<USER>\Desktop\MSP原表\1980_拆分结果"
# 使用时间戳创建唯一的输出文件名
timestamp = time.strftime("%Y%m%d_%H%M%S")
output_file = rf"C:\Users\<USER>\Desktop\MSP原表\合并结果_优化排版_{timestamp}.xlsx"

# 检查目录是否存在
if not os.path.exists(folder_path):
    print(f"目录不存在: {folder_path}")
    exit(1)

# 获取目录中的所有Excel文件（排除~$开头的临时文件）
excel_files = [file for file in glob.glob(os.path.join(folder_path, "*.xlsx")) 
              if not os.path.basename(file).startswith("~$")]
print(f"找到 {len(excel_files)} 个Excel文件")

# 创建一个字典，存储各个店铺和月份的数据
store_data = {}
months_found = set()
stores_found = set()
store_month_data = {}  # 存储每个月份对应的店铺数据
store_mapping = {}     # 存储店铺名称的映射关系
month_store_with_data = {}  # 新增：存储每个月份中有数据的店铺

# 用于记录已处理的问题模板（假设A-E列是一致的问题模板）
question_template = None
dimension_col = None  # 维度列
sub_dim_col = None    # 子维度列
no_col = None         # 序号列
question_col = None   # 问题列
max_score_col = None  # 满分列

# 记录原始数据中维度列和子维度列的值，用于后续恢复格式
dimension_values = {}  # 格式: {行索引: 值}
sub_dimension_values = {}  # 格式: {行索引: 值}

# 定义一个函数来清理店铺名称
def clean_store_name(name):
    # 去除"-ZQ数字"部分
    cleaned = re.sub(r'[-\s]*ZQ\d+\s*', '', name)
    # 去除文件名尾部的空格和特殊字符
    cleaned = re.sub(r'[-\s]+$', '', cleaned)
    return cleaned

# 处理每个Excel文件
for file_idx, file_path in enumerate(excel_files):
    file_name = os.path.basename(file_path)
    print(f"正在处理文件: {file_name}")
    
    try:
        # 从文件名中提取信息
        # 提取月份信息
        month_match = re.search(r'(\d+)月', file_name)
        month = month_match.group(1) if month_match else "未知月份"
        months_found.add(month)
        
        # 提取店铺名称 - 修改店铺名提取方式
        store_match = re.search(r'_([^_]+?)(?:_ZQ\d+|_\d+月)', file_name)
        if not store_match:
            # 如果上述匹配失败，尝试另一种匹配模式
            store_match = re.search(r'_(.+?)_\d+月', file_name)
        
        original_store_name = store_match.group(1) if store_match else "未知店铺"
        
        # 清理店铺名称
        store_name = clean_store_name(original_store_name)
        
        if store_name == "未知店铺":
            print(f"  警告：无法从文件名中提取店铺名称: {file_name}")
            # 尝试从文件内容中提取店铺名称
            try:
                temp_df = pd.read_excel(file_path)
                for col in temp_df.columns:
                    for val in temp_df[col]:
                        if isinstance(val, str) and "店" in val and len(val) > 5:
                            potential_store = val.strip()
                            if "1980" in potential_store:
                                store_name = clean_store_name(potential_store)
                                print(f"  从内容中提取到店铺名: {store_name}")
                                break
                    if store_name != "未知店铺":
                        break
            except:
                pass
        
        # 保存原始店铺名称到清理后名称的映射
        store_mapping[original_store_name] = store_name
        
        # 添加到店铺集合
        stores_found.add(store_name)
        
        # 记录月份和店铺的对应关系
        if month not in store_month_data:
            store_month_data[month] = []
        if store_name not in store_month_data[month]:
            store_month_data[month].append(store_name)
        
        # 初始化月份对应的有数据店铺集合
        if month not in month_store_with_data:
            month_store_with_data[month] = set()
        
        # 读取Excel文件，查找标题行
        df_raw = pd.read_excel(file_path, header=None)
        
        # 查找包含"维度"和"得分"等关键词的标题行
        header_row = None
        for i in range(min(5, df_raw.shape[0])):
            row_values = [str(x).lower() if pd.notna(x) else "" for x in df_raw.iloc[i]]
            row_text = " ".join(row_values)
            if "维度" in row_text and ("no" in row_text or "得分" in row_text):
                header_row = i
                break
        
        if header_row is None:
            print(f"  未找到标题行，跳过文件: {file_name}")
            continue
        
        # 使用标题行重新读取数据
        df = pd.read_excel(file_path, header=header_row)
        
        # 确定关键列
        columns = list(df.columns)
        dim_col = None  # 维度列
        sub_col = None  # 子维度列
        no_c = None     # 序号列
        quest_col = None # 问题列
        max_sc_col = None # 满分列
        score_col = None  # 得分列
        desc_col = None   # 详细描述列
        
        # 识别关键列
        for col in columns:
            col_str = str(col).lower()
            if "维度" in col_str and "子" not in col_str:
                dim_col = col
            elif "子维度" in col_str:
                sub_col = col
            elif col_str == "no" or "编号" in col_str:
                no_c = col
            elif "问题" in col_str:
                quest_col = col
            elif "满分" in col_str:
                max_sc_col = col
            elif "得分" in col_str:
                score_col = col
            elif "详细描述" in col_str or "备注" in col_str:
                desc_col = col
        
        # 确保找到所有需要的列
        if not all([dim_col, no_c, quest_col, max_sc_col, score_col, desc_col]):
            missing_cols = []
            if not dim_col: missing_cols.append("维度列")
            if not no_c: missing_cols.append("序号列")
            if not quest_col: missing_cols.append("问题列")
            if not max_sc_col: missing_cols.append("满分列")
            if not score_col: missing_cols.append("得分列")
            if not desc_col: missing_cols.append("详细描述列")
            print(f"  未找到所有必要的列: {', '.join(missing_cols)}，跳过文件: {file_name}")
            continue
        
        # 保存列引用以便后续使用
        if dimension_col is None:
            dimension_col = dim_col
            sub_dim_col = sub_col
            no_col = no_c
            question_col = quest_col
            max_score_col = max_sc_col
        
        # 清理数据
        df = df.fillna("")
        
        # 如果这是第一个有效文件，记录维度和子维度值，用于恢复格式
        if file_idx == 0 or not dimension_values:
            for i, row in df.iterrows():
                dim_val = row[dim_col] if pd.notna(row[dim_col]) else ""
                sub_dim_val = row[sub_col] if pd.notna(row[sub_col]) else ""
                
                if dim_val:
                    dimension_values[i] = dim_val
                if sub_dim_val:
                    sub_dimension_values[i] = sub_dim_val
        
        # 如果还没有设定问题模板，使用第一个文件作为模板
        if question_template is None:
            # 只保留前5列作为模板（维度、子维度、序号、问题、满分）
            template_cols = [dim_col, sub_col, no_c, quest_col, max_sc_col]
            question_template = df[template_cols].copy()
            
        # 将每行数据与对应的店铺和月份关联
        has_data = False  # 判断当前店铺是否有数据
        
        for _, row in df.iterrows():
            if pd.notna(row[no_c]) and row[no_c] != "":
                no_val = row[no_c]
                
                # 初始化存储结构
                if no_val not in store_data:
                    store_data[no_val] = {}
                
                if store_name not in store_data[no_val]:
                    store_data[no_val][store_name] = {}
                
                # 存储得分和详细描述
                score_val = row[score_col] if pd.notna(row[score_col]) else ""
                desc_val = row[desc_col] if pd.notna(row[desc_col]) else ""
                
                # 只有当得分不为空时才记录该店铺有数据
                if score_val != "":
                    has_data = True
                
                store_data[no_val][store_name][month] = {
                    "得分": score_val,
                    "详细描述": desc_val
                }
        
        # 如果这个店铺在当前月份有数据，添加到跟踪集合中
        if has_data:
            month_store_with_data[month].add(store_name)
                
    except Exception as e:
        print(f"  处理文件 {file_name} 时出错: {str(e)}")

if question_template is not None:
    # 转换为有序列表以便排序
    months_list = sorted([m for m in months_found if m.isdigit()], key=int)
    
    # 打印每个月份中有数据的店铺
    print("\n每月有数据的店铺:")
    for month in months_list:
        if month in month_store_with_data:
            stores_with_data = sorted(month_store_with_data[month])
            print(f"{month}月: {stores_with_data}")
    
    # 处理店铺列表 - 只保留有数据的店铺
    all_stores_with_data = set()
    for month in month_store_with_data:
        all_stores_with_data.update(month_store_with_data[month])
    
    all_stores_with_data = sorted(all_stores_with_data)
    print(f"\n识别到的所有有数据的店铺: {all_stores_with_data}")
    
    # 检查店铺名称映射
    print("\n店铺名称映射关系:")
    for orig, cleaned in store_mapping.items():
        if orig != cleaned and cleaned in all_stores_with_data:
            print(f"  {orig} -> {cleaned}")
    
    # 创建最终数据框架
    final_df = question_template.copy()
    
    # 重命名基本列
    column_mapping = {
        dimension_col: "维度",
        sub_dim_col: "子维度", 
        no_col: "序号",
        question_col: "问题",
        max_score_col: "满分"
    }
    final_df = final_df.rename(columns=column_mapping)
    
    # 保存到Excel
    print(f"\n正在创建优化排版的Excel: {output_file}")
    
    # 创建一个新的工作簿
    workbook = openpyxl.Workbook()
    worksheet = workbook.active
    worksheet.title = "合并报告"
    
    # 设置基础列的宽度
    worksheet.column_dimensions['A'].width = 15  # 维度
    worksheet.column_dimensions['B'].width = 15  # 子维度
    worksheet.column_dimensions['C'].width = 8   # 序号
    worksheet.column_dimensions['D'].width = 40  # 问题
    worksheet.column_dimensions['E'].width = 8   # 满分
    
    # 基础列数量
    base_cols = 5  # A-E列是基础列（维度、子维度、序号、问题、满分）
    
    # 样式设置
    header_font = Font(bold=True, name='微软雅黑', size=10)
    header_fill = PatternFill(start_color="D9E1F2", end_color="D9E1F2", fill_type="solid")
    store_fill = PatternFill(start_color="E2EFDA", end_color="E2EFDA", fill_type="solid")
    border = Border(
        left=Side(style='thin'), 
        right=Side(style='thin'), 
        top=Side(style='thin'), 
        bottom=Side(style='thin')
    )
    center_alignment = Alignment(horizontal='center', vertical='center', wrap_text=True)
    
    # 创建标题行
    # 第1行：月份
    current_col = base_cols + 1
    
    for month in months_list:
        # 获取当前月份中有数据的店铺
        stores_in_month = sorted(month_store_with_data.get(month, set()))
        
        # 每个店铺占两列(得分和详细描述)
        month_cols = len(stores_in_month) * 2
        
        if month_cols > 0:
            # 合并月份单元格
            end_col = current_col + month_cols - 1
            worksheet.merge_cells(start_row=1, start_column=current_col, end_row=1, end_column=end_col)
            month_cell = worksheet.cell(row=1, column=current_col)
            month_cell.value = f"{month}月"
            month_cell.font = header_font
            month_cell.fill = header_fill
            month_cell.border = border
            month_cell.alignment = center_alignment
            
            # 添加店铺行
            for j, store in enumerate(stores_in_month):
                store_col_start = current_col + j * 2
                store_col_end = store_col_start + 1
                
                # 合并店铺名单元格
                worksheet.merge_cells(start_row=2, start_column=store_col_start, end_row=2, end_column=store_col_end)
                store_cell = worksheet.cell(row=2, column=store_col_start)
                store_cell.value = store
                store_cell.font = header_font
                store_cell.fill = store_fill
                store_cell.border = border
                store_cell.alignment = center_alignment
                
                # 第3行：得分和详细描述标题
                score_col = store_col_start
                desc_col = score_col + 1
                
                # 得分列
                score_cell = worksheet.cell(row=3, column=score_col)
                score_cell.value = "得分"
                score_cell.font = header_font
                score_cell.border = border
                score_cell.alignment = center_alignment
                
                # 详细描述列
                desc_cell = worksheet.cell(row=3, column=desc_col)
                desc_cell.value = "详细描述"
                desc_cell.font = header_font
                desc_cell.border = border
                desc_cell.alignment = center_alignment
                
                # 设置列宽
                worksheet.column_dimensions[get_column_letter(score_col)].width = 8
                worksheet.column_dimensions[get_column_letter(desc_col)].width = 30
            
            # 更新当前列位置
            current_col += month_cols
    
    # 合并基础列的标题单元格
    for col in range(1, base_cols + 1):
        worksheet.merge_cells(start_row=1, start_column=col, end_row=3, end_column=col)
        cell = worksheet.cell(row=1, column=col)
        cell.alignment = center_alignment
        cell.font = header_font
        cell.fill = header_fill
        cell.border = border
    
    # 添加基础列标题
    base_titles = ["维度", "子维度", "序号", "问题", "满分"]
    for i, title in enumerate(base_titles, start=1):
        worksheet.cell(row=1, column=i).value = title
    
    # 下面处理数据行
    last_dimension = None
    last_sub_dimension = None
    dimension_start_row = None
    sub_dimension_start_row = None
    
    # 数据从第4行开始
    start_row = 4
    
    # 逐行处理数据
    for i, row in final_df.iterrows():
        row_num = start_row + i
        
        # 处理维度列合并
        if row['维度'] != "":
            # 如果不是第一个维度且有之前的维度待合并
            if last_dimension is not None and dimension_start_row is not None and dimension_start_row < row_num - 1:
                worksheet.merge_cells(start_row=dimension_start_row, start_column=1, end_row=row_num-1, end_column=1)
            
            # 设置新的维度和起始行
            last_dimension = row['维度']
            dimension_start_row = row_num
            
            # 重置子维度信息
            last_sub_dimension = None
            sub_dimension_start_row = None
        
        # 处理子维度列合并
        if row['子维度'] != "":
            # 如果不是第一个子维度且有之前的子维度待合并
            if last_sub_dimension is not None and sub_dimension_start_row is not None and sub_dimension_start_row < row_num - 1:
                worksheet.merge_cells(start_row=sub_dimension_start_row, start_column=2, end_row=row_num-1, end_column=2)
            
            # 设置新的子维度和起始行
            last_sub_dimension = row['子维度']
            sub_dimension_start_row = row_num
        
        # 填充基础列数据
        worksheet.cell(row=row_num, column=1).value = row['维度']
        worksheet.cell(row=row_num, column=2).value = row['子维度']
        worksheet.cell(row=row_num, column=3).value = row['序号']
        worksheet.cell(row=row_num, column=4).value = row['问题']
        worksheet.cell(row=row_num, column=5).value = row['满分']
        
        # 添加边框和样式
        for col in range(1, 6):
            worksheet.cell(row=row_num, column=col).border = border
            if col == 3:  # 序号列居中
                worksheet.cell(row=row_num, column=col).alignment = center_alignment
            elif col in [1, 2, 5]:  # 维度、子维度、满分列居中
                worksheet.cell(row=row_num, column=col).alignment = center_alignment
        
        # 填充得分和详细描述数据
        no_val = row['序号']
        current_col = base_cols + 1
        
        for month in months_list:
            # 获取当前月份中有数据的店铺
            stores_in_month = sorted(month_store_with_data.get(month, set()))
            
            for j, store in enumerate(stores_in_month):
                score_col = current_col + j * 2
                desc_col = score_col + 1
                
                # 获取该行号对应的店铺在该月的数据
                if (no_val in store_data and 
                    store in store_data[no_val] and 
                    month in store_data[no_val][store]):
                    
                    data = store_data[no_val][store][month]
                    
                    # 填充得分
                    score_cell = worksheet.cell(row=row_num, column=score_col)
                    score_cell.value = data["得分"]
                    score_cell.border = border
                    score_cell.alignment = Alignment(horizontal='center', vertical='center')
                    
                    # 填充详细描述
                    desc_cell = worksheet.cell(row=row_num, column=desc_col)
                    desc_cell.value = data["详细描述"]
                    desc_cell.border = border
                    desc_cell.alignment = Alignment(vertical='center', wrap_text=True)
                else:
                    # 即使没有数据也添加边框
                    worksheet.cell(row=row_num, column=score_col).border = border
                    worksheet.cell(row=row_num, column=desc_col).border = border
            
            # 更新当前列位置
            current_col += len(stores_in_month) * 2
    
    # 处理最后一个维度和子维度的合并
    last_row = start_row + len(final_df) - 1
    if dimension_start_row is not None and dimension_start_row < last_row:
        worksheet.merge_cells(start_row=dimension_start_row, start_column=1, end_row=last_row, end_column=1)
    if sub_dimension_start_row is not None and sub_dimension_start_row < last_row:
        worksheet.merge_cells(start_row=sub_dimension_start_row, start_column=2, end_row=last_row, end_column=2)
    
    # 保存工作簿
    workbook.save(output_file)
    
    print("优化排版合并完成!")
else:
    print("没有处理任何有效的Excel文件，合并失败") 